<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.name" clearable class="filter-item" style="width: 160px;" placeholder="请输入果树名称" />
      <el-input v-model="listQuery.brand" clearable class="filter-item" style="width: 160px;" placeholder="请输入品牌" />
      <el-input v-model="listQuery.variety" clearable class="filter-item" style="width: 160px;" placeholder="请输入品种" />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">查找</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-plus" @click="handleCreate" v-hasPermi="['fruit:tree:add']">添加</el-button>
      <el-button class="filter-item" type="danger" icon="el-icon-delete" :disabled="batchDeleteArr.length === 0" @click="handleDeleteRows">批量删除</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" element-loading-text="正在查询中..." border fit highlight-current-row @selection-change="handleSelectionChange">

      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="ID" prop="id" width="80" />

      <el-table-column align="center" label="名称" prop="name" min-width="150" show-overflow-tooltip />

      <el-table-column align="center" label="图片" prop="image" width="80">
        <template slot-scope="scope">
          <el-image v-if="scope.row.image" :src="scope.row.image" :preview-src-list="[scope.row.image]" style="width: 40px; height: 40px" fit="cover" />
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="价格" prop="price" width="100">
        <template slot-scope="scope">
          ¥{{ scope.row.price }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="总库存" prop="totalStock" width="100" />

      <el-table-column align="center" label="已售" prop="soldStock" width="80" />

      <el-table-column align="center" label="品牌" prop="brand" width="120" show-overflow-tooltip />

      <el-table-column align="center" label="品种" prop="variety" width="100" show-overflow-tooltip />

      <el-table-column align="center" label="认养周期" prop="period" width="100">
        <template slot-scope="scope">
          {{ scope.row.period }}个月
        </template>
      </el-table-column>

      <el-table-column align="center" label="状态" prop="marketEnable" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.marketEnable === '1' ? 'success' : 'danger'">
            {{ scope.row.marketEnable === '1' ? '已上架' : '已下架' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="280" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleUpdate(scope.row)" v-hasPermi="['fruit:tree:edit']">编辑</el-button>
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.marketEnable === '0'" size="mini" type="text" @click="handleUp(scope.row)" v-hasPermi="['fruit:tree:edit']">上架</el-button>
          <el-button v-if="scope.row.marketEnable === '1'" size="mini" type="text" @click="handleUnder(scope.row)" v-hasPermi="['fruit:tree:edit']">下架</el-button>
          <el-button size="mini" type="text" @click="handleDelete(scope.row)" v-hasPermi="['fruit:tree:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <!-- 果树详情对话框 -->
    <el-dialog title="果树详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="名称">{{ detailData.name }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ detailData.brand }}</el-descriptions-item>
        <el-descriptions-item label="品种">{{ detailData.variety }}</el-descriptions-item>
        <el-descriptions-item label="价格">¥{{ detailData.price }}</el-descriptions-item>
        <el-descriptions-item label="总库存">{{ detailData.totalStock }}</el-descriptions-item>
        <el-descriptions-item label="已售">{{ detailData.soldStock }}</el-descriptions-item>
        <el-descriptions-item label="认养周期">{{ detailData.period }}个月</el-descriptions-item>
        <el-descriptions-item label="产地">{{ detailData.origin }}</el-descriptions-item>
        <el-descriptions-item label="规格包装">{{ detailData.specPackage }}</el-descriptions-item>
        <el-descriptions-item label="存储条件">{{ detailData.storageCondition }}</el-descriptions-item>
        <el-descriptions-item label="保质期">{{ detailData.shelflife }}</el-descriptions-item>
        <el-descriptions-item label="单果直径">{{ detailData.diameter }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailData.description }}</el-descriptions-item>
        <el-descriptions-item label="认养权益" :span="2">
          <div v-if="detailData.benefits">
            <div v-for="(benefit, index) in detailData.benefits.split('\n')" :key="index">{{ benefit }}</div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
      <div v-if="detailData.image" style="margin-top: 20px;">
        <h4>主图片</h4>
        <el-image :src="detailData.image" style="width: 200px; height: 200px" fit="cover" />
      </div>
      <div v-if="detailData.bannerImages" style="margin-top: 20px;">
        <h4>轮播图片</h4>
        <el-image
          v-for="(img, index) in detailData.bannerImages.split(',')"
          :key="index"
          :src="img.trim()"
          style="width: 100px; height: 100px; margin-right: 10px"
          fit="cover"
        />
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listFruitTree, getFruitTree, delFruitTree, upFruitTree, underFruitTree } from '@/api/fruit/tree'
import Pagination from '@/components/Pagination'

export default {
  name: 'FruitTreeList',
  components: { Pagination },
  data() {
    return {
      batchDeleteArr: [],
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        brand: undefined,
        variety: undefined
      },
      detailVisible: false,
      detailData: {}
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      listFruitTree(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.pageNum = 1
      this.getList()
    },
    handleCreate() {
      this.$router.push({ path: '/tree/add' })
    },
    handleUpdate(row) {
      this.$router.push({ path: '/tree/edit', query: { id: row.id }})
    },
    handleDetail(row) {
      getFruitTree(row.id).then(response => {
        this.detailData = response.data
        this.detailVisible = true
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },
    handleDelete(row) {
      this.$confirm('确定删除果树"' + row.name + '"吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delFruitTree(row.id).then(response => {
          this.$message.success('删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    handleUp(row) {
      this.$confirm('确定上架果树"' + row.name + '"吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        upFruitTree(row.id).then(response => {
          this.$message.success('上架成功')
          this.getList()
        }).catch(() => {
          this.$message.error('上架失败')
        })
      }).catch(() => {})
    },
    handleUnder(row) {
      this.$confirm('确定下架果树"' + row.name + '"吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        underFruitTree(row.id).then(response => {
          this.$message.success('下架成功')
          this.getList()
        }).catch(() => {
          this.$message.error('下架失败')
        })
      }).catch(() => {})
    },
    handleSelectionChange(val) {
      this.batchDeleteArr = val
    },
    handleDeleteRows() {
      if (this.batchDeleteArr.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('确定删除选中的 ' + this.batchDeleteArr.length + ' 条数据吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const promises = this.batchDeleteArr.map(row => delFruitTree(row.id))
        Promise.all(promises).then(() => {
          this.$message.success('批量删除成功')
          this.getList()
        }).catch(() => {
          this.$message.error('批量删除失败')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}
.filter-item {
  margin-right: 10px;
}
</style>
