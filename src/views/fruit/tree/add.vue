<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="果树名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入果树名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="品种" prop="variety">
              <el-input v-model="form.variety" placeholder="请输入品种" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类型" prop="productType">
              <el-input v-model="form.productType" placeholder="请输入产品类型" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入果树描述" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>价格库存</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="价格" prop="price">
              <el-input-number v-model="form.price" :min="0" :precision="2" placeholder="请输入价格" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总库存" prop="totalStock">
              <el-input-number v-model="form.totalStock" :min="0" placeholder="请输入总库存" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="已售库存" prop="soldStock">
              <el-input-number v-model="form.soldStock" :min="0" placeholder="请输入已售库存" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="认养周期" prop="period">
              <el-select v-model="form.period" placeholder="请选择认养周期" style="width: 100%">
                <el-option label="6个月" :value="6" />
                <el-option label="12个月" :value="12" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运费" prop="freightPrice">
              <el-input-number v-model="form.freightPrice" :min="0" :precision="2" placeholder="请输入运费" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="上架状态" prop="marketEnable">
              <el-switch
                v-model="form.marketEnable"
                active-text="上架"
                inactive-text="下架"
                active-value="UPPER"
                inactive-value="DOWN"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>产品规格</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产地" prop="origin">
              <el-input v-model="form.origin" placeholder="请输入产地" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单果直径" prop="diameter">
              <el-input v-model="form.diameter" placeholder="请输入单果直径" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格包装" prop="specPackage">
              <el-input v-model="form.specPackage" placeholder="请输入规格包装" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存储条件" prop="storageCondition">
              <el-input v-model="form.storageCondition" placeholder="请输入存储条件" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="保质期" prop="shelflife">
          <el-input v-model="form.shelflife" placeholder="请输入保质期" style="width: 300px" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>认养权益</span>
        </div>

        <el-form-item label="权益说明" prop="benefits">
          <el-input v-model="form.benefits" type="textarea" :rows="4" placeholder="请输入认养权益，每行一个权益" />
        </el-form-item>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>图片信息</span>
        </div>

        <el-form-item label="主图片">
          <el-upload
            class="avatar-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
            accept="image/*"
          >
            <img v-if="form.image" :src="form.image" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>

        <el-form-item label="轮播图片">
          <el-upload
            class="upload-demo"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :on-success="handleBannerSuccess"
            :on-remove="handleBannerRemove"
            :file-list="bannerFileList"
            list-type="picture-card"
            accept="image/*"
          >
            <i class="el-icon-plus"></i>
          </el-upload>
        </el-form-item>
      </el-card>

      <div style="text-align: center; margin-top: 20px;">
        <el-button @click="cancel">取消</el-button>
        <el-button type="info" @click="quickFill">快速填充测试数据</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { addFruitTree } from '@/api/fruit/tree'
import { getToken } from '@/utils/auth'

export default {
  name: 'FruitTreeAdd',
  data() {
    return {
      form: {
        name: '',
        description: '',
        image: '',
        price: 0,
        totalStock: 0,
        soldStock: 0,
        brand: '',
        productType: '',
        diameter: '',
        shelflife: '',
        origin: '',
        specPackage: '',
        storageCondition: '',
        variety: '',
        benefits: '',
        period: 6,
        bannerImages: '',
        freightPrice: 0,
        marketEnable: 'DOWN'
      },
      rules: {
        name: [
          { required: true, message: '果树名称不能为空', trigger: 'blur' }
        ],
        brand: [
          { required: true, message: '品牌不能为空', trigger: 'blur' }
        ],
        variety: [
          { required: true, message: '品种不能为空', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '价格不能为空', trigger: 'blur' }
        ],
        totalStock: [
          { required: true, message: '总库存不能为空', trigger: 'blur' }
        ],
        period: [
          { required: true, message: '认养周期不能为空', trigger: 'change' }
        ]
      },
      bannerFileList: [],
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      }
    }
  },
  methods: {
    handleImageSuccess(response) {
      if (response.code === 0) {
        this.form.image = response.url || response.data?.url
      } else {
        this.$message.error('上传失败')
      }
    },
    handleBannerSuccess(response, file, fileList) {
      if (response.code === 0) {
        const url = response.url || response.data?.url
        this.bannerFileList = fileList.map(item => ({
          name: item.name,
          url: item.response ? (item.response.url || item.response.data?.url) : item.url
        }))
        this.updateBannerImages()
      } else {
        this.$message.error('上传失败')
      }
    },
    handleBannerRemove(file, fileList) {
      this.bannerFileList = fileList
      this.updateBannerImages()
    },
    updateBannerImages() {
      this.form.bannerImages = this.bannerFileList.map(item => item.url).join(',')
    },
    beforeImageUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
      }
      return isImage && isLt2M
    },
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          addFruitTree(this.form).then(() => {
            this.$modal.msgSuccess("新增成功")
            // 通知列表页面刷新
            this.$eventBus.$emit('fruitTreeListRefresh')
            this.cancel()
          }).catch(() => {
            this.$modal.msgError("新增失败")
          })
        }
      })
    },
    cancel() {
      this.$router.push('/tree/list')
    },
    // 快速填充测试数据
    quickFill() {
      this.form = {
        name: '富士苹果树-2025年度认养（测试）',
        description: '认养专属富士苹果树，体验从开花到结果的完整生长周期。每棵树保证产量不低于50斤，可远程观看生长直播，收获季免费配送上门。',
        image: 'http://fruit.songtaiwang.cn/profile/upload/2025/08/02/wechat_2025-08-02_143716_467_20250802143756A001.png',
        price: 1999.0,
        totalStock: 100,
        soldStock: 0,
        brand: '安果礼',
        productType: '食用农产品',
        diameter: '5-8cm（树干直径）',
        shelflife: '冷藏保存30天',
        origin: '陕西延安',
        specPackage: '10kg/箱（预计单树产量）',
        storageCondition: '0-4℃冷藏',
        variety: '红富士',
        benefits: '专属树牌+生长监测+免费采摘体验+定制礼盒包装',
        period: 12,
        bannerImages: 'http://fruit.songtaiwang.cn/profile/upload/2025/08/02/wechat_2025-08-02_143716_467_20250802143756A001.png',
        freightPrice: 0.0,
        marketEnable: 'UPPER'
      }

      // 更新轮播图片列表
      if (this.form.bannerImages) {
        this.bannerFileList = this.form.bannerImages.split(',').map((url, index) => ({
          name: `banner-${index}`,
          url: url.trim()
        }))
      }

      this.$message.success('测试数据填充完成')
    }
  }
}
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 148px;
  height: 148px;
  line-height: 148px;
  text-align: center;
}

.avatar {
  width: 148px;
  height: 148px;
  display: block;
}
</style>
