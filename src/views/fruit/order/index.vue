<template>
  <div class="app-container">
    <el-form :model="listQuery" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="订单号" prop="orderSn">
        <el-input
          v-model="listQuery.orderSn"
          placeholder="请输入订单号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="果树名称" prop="fruitTreeName">
        <el-input
          v-model="listQuery.fruitTreeName"
          placeholder="请输入果树名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input
          v-model="listQuery.contactName"
          placeholder="请输入联系人"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleFilter"
        />
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select
          v-model="listQuery.status"
          placeholder="订单状态"
          clearable
          style="width: 240px"
        >
          <el-option label="待付款" :value="0" />
          <el-option label="待发货" :value="10" />
          <el-option label="已发货" :value="20" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleFilter">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list">

      <el-table-column align="center" label="订单号" prop="orderSn" min-width="180" show-overflow-tooltip />

      <el-table-column align="center" label="果树名称" prop="fruitTreeName" min-width="150" show-overflow-tooltip />

      <el-table-column align="center" label="联系人" prop="contactName" width="100" />

      <el-table-column align="center" label="手机号" prop="phone" width="120" />

      <el-table-column align="center" label="总价" prop="totalPrice" width="100">
        <template slot-scope="scope">
          ¥{{ scope.row.totalPrice }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="订单状态" prop="status" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="支付状态" prop="payStatus" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.payStatus" :type="scope.row.payStatus === '1' ? 'success' : 'danger'">
            {{ scope.row.payStatus === '1' ? '已支付' : '未支付' }}
          </el-tag>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="下单时间" prop="createTime" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="handleDetail(scope.row)">详情</el-button>
          <el-button v-if="scope.row.status === 10" size="mini" type="text" @click="handleDelivery(scope.row)" v-hasPermi="['fruit:order:delivery']">发货</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNum" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" :visible.sync="detailVisible" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单号">{{ detailData.orderSn }}</el-descriptions-item>
        <el-descriptions-item label="果树名称">{{ detailData.fruitTreeName }}</el-descriptions-item>
        <el-descriptions-item label="单价">¥{{ detailData.unitPrice }}</el-descriptions-item>
        <el-descriptions-item label="数量">{{ detailData.fruitNum }}</el-descriptions-item>
        <el-descriptions-item label="运费">¥{{ detailData.freightPrice }}</el-descriptions-item>
        <el-descriptions-item label="总价">¥{{ detailData.totalPrice }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ getStatusText(detailData.status) }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">{{ detailData.payStatus === '1' ? '已支付' : '未支付' }}</el-descriptions-item>
        <el-descriptions-item label="支付方式">{{ detailData.paymentMethod || '-' }}</el-descriptions-item>
        <el-descriptions-item label="支付时间">{{ parseTime(detailData.paymentTime) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="联系人">{{ detailData.contactName }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detailData.phone }}</el-descriptions-item>
        <el-descriptions-item label="收货地址" :span="2">
          {{ detailData.province }}{{ detailData.city }}{{ detailData.district }}{{ detailData.detailAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="标签名称">{{ detailData.labelName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="心愿寄语">{{ detailData.wishMsg || '-' }}</el-descriptions-item>
        <el-descriptions-item label="认养时间">{{ parseTime(detailData.adoptTime) }}</el-descriptions-item>
        <el-descriptions-item label="认养人">{{ detailData.adoptBy }}</el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ parseTime(detailData.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ parseTime(detailData.updateTime) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 发货对话框 -->
    <el-dialog title="订单发货" :visible.sync="deliveryVisible" width="500px" append-to-body>
      <el-form ref="deliveryForm" :model="deliveryForm" :rules="deliveryRules" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="deliveryForm.orderSn" disabled />
        </el-form-item>
        <el-form-item label="物流公司" prop="logisticsCode">
          <el-select v-model="deliveryForm.logisticsCode" placeholder="请选择物流公司" style="width: 100%" @change="handleLogisticsChange">
            <el-option
              v-for="item in logisticsList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="logisticsNo">
          <el-input v-model="deliveryForm.logisticsNo" placeholder="请输入物流单号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deliveryVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDelivery">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listFruitOrder, getFruitOrder, deliveryOrder } from '@/api/fruit/order'
import { getDictDataByType } from '@/api/system/dict/data'
import Pagination from '@/components/Pagination'
import { parseTime } from '@/utils/ruoyi'

export default {
  name: 'FruitOrderList',
  components: { Pagination },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 订单表格数据
      list: [],
      // 查询参数
      listQuery: {
        pageNum: 1,
        pageSize: 10,
        orderSn: undefined,
        fruitTreeName: undefined,
        contactName: undefined,
        status: undefined
      },
      detailVisible: false,
      detailData: {},
      deliveryVisible: false,
      deliveryForm: {
        orderSn: '',
        logisticsCode: '',
        logisticsNo: '',
        logisticsName: ''
      },
      deliveryRules: {
        logisticsCode: [
          { required: true, message: '请选择物流公司', trigger: 'change' }
        ],
        logisticsNo: [
          { required: true, message: '请输入物流单号', trigger: 'blur' }
        ]
      },
      logisticsList: []
    }
  },
  created() {
    this.getList()
    this.getLogisticsList()
  },
  methods: {
    parseTime,
    /** 查询订单列表 */
    getList() {
      this.loading = true
      listFruitOrder(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.loading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.loading = false
      })
    },
    getLogisticsList() {
      getDictDataByType('logistics_company').then(response => {
        this.logisticsList = response.data
      })
    },
    /** 搜索按钮操作 */
    handleFilter() {
      this.listQuery.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.$refs.queryForm.resetFields()
      this.handleFilter()
    },
    handleDetail(row) {
      getFruitOrder(row.id).then(response => {
        this.detailData = response.data
        this.detailVisible = true
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    },
    handleDelivery(row) {
      this.deliveryForm = {
        orderSn: row.orderSn,
        logisticsCode: '',
        logisticsNo: '',
        logisticsName: ''
      }
      this.deliveryVisible = true
    },
    handleLogisticsChange(value) {
      const logistics = this.logisticsList.find(item => item.dictValue === value)
      if (logistics) {
        this.deliveryForm.logisticsName = logistics.dictLabel
      }
    },
    submitDelivery() {
      this.$refs.deliveryForm.validate(valid => {
        if (valid) {
          deliveryOrder(this.deliveryForm).then(() => {
            this.$message.success('发货成功')
            this.deliveryVisible = false
            this.getList()
          }).catch(() => {
            this.$message.error('发货失败')
          })
        }
      })
    },
    getStatusText(status) {
      const statusMap = {
        0: '待付款',
        10: '待发货',
        20: '已发货'
      }
      return statusMap[status] || '未知'
    },
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        10: 'success',
        20: 'primary'
      }
      return typeMap[status] || 'info'
    }
  }
}
</script>


