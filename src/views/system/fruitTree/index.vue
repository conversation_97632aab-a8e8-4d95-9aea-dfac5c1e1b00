<template>
  <div class="app-container">

    <!-- 查询和其他操作 -->
    <div class="filter-container">
      <el-input v-model="listQuery.goodsId" clearable class="filter-item" style="width: 160px;" :placeholder="'请输入商品ID'" />
      <el-input v-model="listQuery.goodsSn" clearable class="filter-item" style="width: 160px;" :placeholder="'请输入商品编号'" />
      <el-input v-model="listQuery.name" clearable class="filter-item" style="width: 160px;" :placeholder="'请输入商品名称'" />
      <el-button class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ '查找' }}</el-button>
      <el-button class="filter-item" type="primary" icon="el-icon-edit" @click="handleCreate" v-hasPermi="['system:menu:add']">{{ '添加' }}</el-button>
      <el-button :loading="downloadLoading" class="filter-item" type="primary" icon="el-icon-download" @click="handleDownload">{{ '导出' }}</el-button>
      <el-button class="filter-item" type="danger" icon="el-icon-delete" :disabled="batchDeleteArr.length === 0" @click="handleDeleteRows">{{ '批量删除' }}</el-button>
    </div>

    <!-- 查询结果 -->
    <el-table v-loading="listLoading" :data="list" :element-loading-text="'正在查询中。。。'" border fit highlight-current-row @selection-change="handleSelectionChange">

      <el-table-column type="selection" width="55" />
      <el-table-column align="center" :label="'果树ID'" prop="id" />

      <el-table-column align="center"  :label="'名称'" prop="name" />

      <el-table-column align="center" min-width="100" :label="'描述'" prop="description" />

      <el-table-column align="center" property="image" :label="'图片'">
        <template slot-scope="scope">
          <el-image :src="thumbnail('http://yanxuan.nosdn.127.net/37bc0fa3524a904ac740340fa92bd515.png')" :preview-src-list="toPreview1(scope.row, 'http://yanxuan.nosdn.127.net/37bc0fa3524a904ac740340fa92bd515.png')" style="width: 40px; height: 40px" />
        </template>
      </el-table-column>

      <el-table-column align="center"  :label="'价格'" prop="price" />

      <el-table-column align="center"  :label="'总库存数'" prop="total_stock" />

      <el-table-column align="center"  :label="'已售库存数'" prop="total_stock" />

      <el-table-column align="center"  :label="'品牌'" prop="brand" />

      <el-table-column align="center"  :label="'产品类型'" prop="product_type" />

      <el-table-column align="center"  :label="'单果直径'" prop="diameter" />

      <el-table-column align="center"  :label="'保质期'" prop="shelf_life" />

      <el-table-column align="center"  :label="'产地'" prop="origin" />

      <el-table-column align="center"  :label="'规格包装'" prop="spec_package" />

      <el-table-column align="center"  :label="'存储条件'" prop="storage_condition" />

      <el-table-column align="center"  :label="'品种'" prop="variety" />

      <el-table-column align="center" :label="'操作'" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary"   @click="handleUpdate(scope.row)">{{ '编辑' }}</el-button>
          <el-button type="danger"  @click="handleDelete(scope.row)">{{ '删除' }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <el-tooltip placement="top" :content="返回顶部">
      <back-to-top :visibility-height="100" />
    </el-tooltip>

  </div>
</template>

<style>
  .table-expand {
    font-size: 0;
    padding-left: 60px;
  }
  .table-expand label {
    width: 100px;
    color: #99a9bf;
  }
  .table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
  }
  .gallery {
    width: 80px;
    margin-right: 10px;
  }
  .goods-detail-box img {
    width: 100%;
  }
</style>

<script>
import { listFruitTree, getFruitTree,addFruitTree,updateFruitTree,delFruitTree } from '@/api/system/fruitTree'
import Pagination from '@/components/Pagination' // Secondary package based on el-pagination
import { thumbnail, toPreview } from '@/utils/index'

export default {
  name: 'GoodsList',
  components: {  Pagination },
  data() {
    return {
      batchDeleteArr: [],
      thumbnail,
      toPreview,
      list: [],
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        goodsSn: undefined,
        name: undefined,
        sort: 'create_time',
        order: 'desc'
      },
      goodsDetail: '',
      detailDialogVisible: false,
      downloadLoading: false
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.listLoading = true
      listFruitTree(this.listQuery).then(response => {
        this.list = response.rows
        this.total = response.total
        this.listLoading = false
      }).catch(() => {
        this.list = []
        this.total = 0
        this.listLoading = false
      })
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      //this.reset()
      this.getTreeselect()
      if (row != null && row.menuId) {
        this.form.parentId = row.menuId
      } else {
        this.form.parentId = 0
      }
      this.open = true
      this.title = "添加菜单"
    },
    handleCreate() {
      this.$router.push({ path: '/system/fruitTree/add' })
    },
    handleUpdate(row) {
      this.$router.push({ path: '/goods/edit', query: { id: row.id }})
    },
    showDetail(detail) {
      this.goodsDetail = detail
      this.detailDialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定删除?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        deleteGoods(row).then(response => {
          this.$notify.success({
            title: '成功',
            message: '删除成功'
          })
          this.getList()
        }).catch(response => {
          this.$notify.error({
            title: '失败',
            message: response.data.errmsg
          })
        })
      }).catch(() => {})
    },
    handleSelectionChange(val) {
      console.log(val)
      this.batchDeleteArr = val
    },
    handleDeleteRows() {
      this.batchDeleteArr.forEach(row => this.handleDeleteEachRow(row))
      this.getList()
    },
    handleDeleteEachRow(row) {
      deleteGoods(row).then(response => {
        this.$notify.success({
          title: '成功',
          message: '删除成功'
        })
      }).catch(response => {
        this.$notify.error({
          title: '失败',
          message: response.data.errmsg
        })
      })
    },

    toPreview1(item, url) {
      item.preview = [url]
      return item.preview
    }
  }
}


</script>
