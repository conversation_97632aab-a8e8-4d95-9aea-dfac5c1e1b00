import request from '@/utils/request'

// 查询果树信息列表
export function listFruitTree(query) {
  return request({
    url: '/system/fruitTree/list',
    method: 'get',
    params: query
  })
}

// 查询果树信息详细
export function getFruitTree(id) {
  return request({
    url: '/system/fruitTree/' + id,
    method: 'get'
  })
}

// 新增果树信息
export function addFruitTree(data) {
  return request({
    url: '/system/fruitTree/add',
    method: 'post',
    data: data
  })
}

// 修改果树信息
export function updateFruitTree(data) {
  return request({
    url: '/system/fruitTree/update',
    method: 'put',
    data: data
  })
}

// 删除果树信息
export function delFruitTree(id) {
  return request({
    url: '/system/fruitTree/' + id,
    method: 'delete'
  })
}

// 果树上架
export function upFruitTree(id) {
  return request({
    url: '/system/fruitTree/up/' + id,
    method: 'put',
    data: {}
  })
}

// 果树下架
export function underFruitTree(id) {
  return request({
    url: '/system/fruitTree/under/' + id,
    method: 'put',
    data: {}
  })
}

// 上传文件
export function uploadFile(data) {
  return request({
    url: '/common/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
