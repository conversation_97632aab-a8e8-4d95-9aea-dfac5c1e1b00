import request from '@/utils/request'

// 查询果树信息列表
export function listFruitTree(query) {
  return request({
    url: '/system/fruitTree/list',
    method: 'get',
    params: query
  })
}

// 查询果树信息详细
export function getFruitTree(id) {
  return request({
    url: '/system/fruitTree/' + id,
    method: 'get'
  })
}

// 新增果树信息
export function addFruitTree(data) {
  return request({
    url: '/system/fruitTree',
    method: 'post',
    data: data
  })
}

// 修改果树信息
export function updateFruitTree(data) {
  return request({
    url: '/system/fruitTree',
    method: 'put',
    data: data
  })
}

// 删除果树信息
export function delFruitTree(id) {
  return request({
    url: '/system/fruitTree/' + id,
    method: 'delete'
  })
}
