import request from '@/utils/request'

// 查询订单列表
export function listFruitOrder(query) {
  return request({
    url: '/system/fruitOrder/list',
    method: 'get',
    params: query
  })
}

// 查询订单详情
export function getFruitOrder(id) {
  return request({
    url: '/system/fruitOrder/' + id,
    method: 'get'
  })
}

// 订单发货
export function deliveryOrder(data) {
  return request({
    url: '/system/fruitOrder/delivery',
    method: 'post',
    data: data
  })
}
